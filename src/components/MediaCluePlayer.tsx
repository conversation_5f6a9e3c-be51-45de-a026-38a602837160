import React, { useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { ClueMedia } from '@/types/crossword';

interface MediaCluePlayerProps {
  clue: ClueMedia;
  wordNumber: number;
  direction: 'across' | 'down';
  onPlayComplete?: () => void;
}

export const MediaCluePlayer = ({
  clue,
  wordNumber,
  direction,
  onPlayComplete
}: MediaCluePlayerProps) => {
  const mediaRef = useRef<HTMLAudioElement | HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  const handlePlayPause = () => {
    if (!mediaRef.current) return;

    if (isPlaying) {
      mediaRef.current.pause();
    } else {
      mediaRef.current.play().catch(console.error);
    }
    setIsPlaying(!isPlaying);
  };

  const handleReplay = () => {
    if (!mediaRef.current) return;
    mediaRef.current.currentTime = Math.max(0, mediaRef.current.currentTime - 15);
  };

  const handleTimeUpdate = () => {
    if (!mediaRef.current) return;
    setCurrentTime(mediaRef.current.currentTime);
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    onPlayComplete?.();
  };

  const progressPercentage = (currentTime / clue.duration) * 100;
  const remainingTime = Math.ceil(clue.duration - currentTime);

  return (
    <div className="bg-gray-900 p-4 border-t border-gray-700">
      <div className="flex items-center justify-between text-white">
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePlayPause}
          className="text-white hover:bg-gray-700"
        >
          {isPlaying ? (
            <Pause className="h-5 w-5" />
          ) : (
            <Play className="h-5 w-5" />
          )}
        </Button>
        
        <div className="text-sm">
          {Math.floor(currentTime / 60)}:{(Math.floor(currentTime) % 60).toString().padStart(2, '0')}
        </div>
        
        <div className="flex-1 mx-4">
          <div className="bg-gray-700 h-1 rounded-full">
            <div 
              className="bg-blue-500 h-1 rounded-full transition-all duration-300" 
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
        
        <div className="text-sm">
          -{Math.floor(remainingTime / 60)}:{(remainingTime % 60).toString().padStart(2, '0')}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReplay}
          className="text-white hover:bg-gray-700 ml-2"
        >
          <RotateCcw className="h-4 w-4" />
          <span className="text-xs ml-1">15</span>
        </Button>
      </div>
      
      <div className="hidden">
        {clue.type === 'audio' ? (
          <audio
            ref={mediaRef as React.RefObject<HTMLAudioElement>}
            src={clue.src}
            onTimeUpdate={handleTimeUpdate}
            onEnded={handleEnded}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
        ) : (
          <video
            ref={mediaRef as React.RefObject<HTMLVideoElement>}
            src={clue.src}
            onTimeUpdate={handleTimeUpdate}
            onEnded={handleEnded}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
        )}
      </div>
    </div>
  );
};