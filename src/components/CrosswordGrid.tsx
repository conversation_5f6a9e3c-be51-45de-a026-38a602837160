import React, { useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CrosswordCell, CrosswordWord } from '@/types/crossword';
import { Play, Pause, Volume2 } from 'lucide-react';

interface CrosswordGridProps {
  cells: CrosswordCell[][];
  words: CrosswordWord[];
  selectedCell: { row: number; col: number } | null;
  selectedWord: CrosswordWord | null;
  onCellClick: (row: number, col: number) => void;
}

export const CrosswordGrid = ({
  cells,
  words,
  selectedCell,
  selectedWord,
  onCellClick
}: CrosswordGridProps) => {
  const gridSize = cells.length;
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string | null>(null);

  // Update audio when selected word changes
  useEffect(() => {
    if (selectedWord && selectedWord.clue.src !== currentAudioUrl) {
      setCurrentAudioUrl(selectedWord.clue.src);
      setIsPlaying(false);

      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    }
  }, [selectedWord, currentAudioUrl]);

  const handleAudioToggle = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent cell selection

    if (!audioRef.current || !selectedWord) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        console.log('🎵 Playing audio:', selectedWord.clue.src);

        // Reset audio to beginning
        audioRef.current.currentTime = 0;

        // Attempt to play
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          await playPromise;
          setIsPlaying(true);
          console.log('✅ Audio playing successfully');
        }
      }
    } catch (error) {
      console.error('❌ Audio playback error:', error);
      setIsPlaying(false);

      // Show user-friendly error
      alert(`Audio playback failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const handleAudioError = (e: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    const audio = e.currentTarget;
    const error = audio.error;

    console.error('❌ Audio loading error:', {
      code: error?.code,
      message: error?.message,
      url: audio.src
    });

    setIsPlaying(false);
  };

  const handleAudioLoad = () => {
    console.log('✅ Audio loaded successfully:', selectedWord?.clue.src);
  };

  // Check if a cell should show the audio control
  const shouldShowAudioControl = (rowIndex: number, colIndex: number): boolean => {
    if (!selectedWord) return false;

    // Show audio control in the first cell of the selected word
    return selectedWord.startRow === rowIndex && selectedWord.startCol === colIndex;
  };
  
  return (
    <div className="crossword-grid mx-auto w-full max-w-lg">
      <div 
        className="grid gap-px p-2 bg-gray-800 border border-gray-600"
        style={{
          gridTemplateColumns: `repeat(${gridSize}, minmax(32px, 1fr))`,
          gridTemplateRows: `repeat(${gridSize}, minmax(32px, 1fr))`,
          aspectRatio: '1'
        }}
      >
        {cells.map((row, rowIndex) =>
          row.map((cell, colIndex) => {
            const showAudioControl = shouldShowAudioControl(rowIndex, colIndex);

            return (
              <div
                key={`${rowIndex}-${colIndex}`}
                className={cn(
                  "relative w-full h-full min-h-[32px] cursor-pointer transition-all duration-200",
                  "flex items-center justify-center text-base font-medium",
                  cell.isBlocked
                    ? "bg-black"
                    : "bg-white hover:bg-gray-100 border border-gray-300",
                  cell.isSelected && "bg-blue-200 border-blue-400",
                  cell.isHighlighted && !cell.isSelected && "bg-blue-100",
                  showAudioControl && "bg-red-100 border-red-400 border-2"
                )}
                onClick={() => !cell.isBlocked && onCellClick(rowIndex, colIndex)}
              >
                {!cell.isBlocked && (
                  <>
                    {cell.number && !showAudioControl && (
                      <span className="absolute top-1 left-1 text-xs text-black font-bold leading-none">
                        {cell.number}
                      </span>
                    )}

                    {showAudioControl ? (
                      <button
                        onClick={handleAudioToggle}
                        className={cn(
                          "w-full h-full flex items-center justify-center",
                          "hover:bg-red-200 transition-colors duration-200",
                          "text-red-600"
                        )}
                        title={`${isPlaying ? 'Pause' : 'Play'} audio clue`}
                      >
                        {isPlaying ? (
                          <Pause className="h-5 w-5" />
                        ) : (
                          <Play className="h-5 w-5" />
                        )}
                        {cell.number && (
                          <span className="absolute top-0 left-0 text-xs text-red-600 font-bold leading-none">
                            {cell.number}
                          </span>
                        )}
                      </button>
                    ) : (
                      <span className="text-black font-bold text-lg">
                        {cell.letter}
                      </span>
                    )}
                  </>
                )}
              </div>
            );
          })
        )}

        {/* Hidden audio element */}
        {selectedWord && (
          <audio
            ref={audioRef}
            src={selectedWord.clue.src}
            onEnded={handleAudioEnded}
            onError={handleAudioError}
            onLoadedData={handleAudioLoad}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            preload="metadata"
            crossOrigin="anonymous"
          />
        )}
      </div>
    </div>
  );
};