import React, { useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CrosswordCell, CrosswordWord } from '@/types/crossword';
import { Play, Pause, Volume2 } from 'lucide-react';

interface CrosswordGridProps {
  cells: CrosswordCell[][];
  words: CrosswordWord[];
  selectedCell: { row: number; col: number } | null;
  selectedWord: CrosswordWord | null;
  onCellClick: (row: number, col: number) => void;
  onPuzzleComplete?: () => void;
}

export const CrosswordGrid = ({
  cells,
  words,
  selectedCell,
  selectedWord,
  onCellClick,
  onPuzzleComplete
}: CrosswordGridProps) => {
  const gridSize = cells.length;
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);

  // Update audio when selected word changes
  useEffect(() => {
    if (selectedWord && selectedWord.clue.src !== currentAudioUrl) {
      setCurrentAudioUrl(selectedWord.clue.src);
      setIsPlaying(false);

      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    }
  }, [selectedWord, currentAudioUrl]);

  const handleAudioToggle = async (e: React.MouseEvent, word: CrosswordWord) => {
    e.stopPropagation(); // Prevent cell selection

    if (!audioRef.current) return;

    try {
      // If this word is already playing, pause it
      if (isPlaying && selectedWord?.id === word.id) {
        audioRef.current.pause();
        setIsPlaying(false);
        return;
      }

      // Set the new audio source and play
      console.log('🎵 Playing audio:', word.clue.src);

      // Update current audio URL and selected word
      setCurrentAudioUrl(word.clue.src);

      // Reset audio to beginning
      audioRef.current.currentTime = 0;

      // Attempt to play
      const playPromise = audioRef.current.play();

      if (playPromise !== undefined) {
        await playPromise;
        setIsPlaying(true);
        console.log('✅ Audio playing successfully');
      }
    } catch (error) {
      console.error('❌ Audio playback error:', error);
      setIsPlaying(false);

      // Show user-friendly error
      alert(`Audio playback failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const handleAudioError = (e: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    const audio = e.currentTarget;
    const error = audio.error;

    console.error('❌ Audio loading error:', {
      code: error?.code,
      message: error?.message,
      url: audio.src
    });

    setIsPlaying(false);
  };

  const handleAudioLoad = () => {
    console.log('✅ Audio loaded successfully:', currentAudioUrl);
  };

  // Check if a cell should show the audio control
  const shouldShowAudioControl = (rowIndex: number, colIndex: number): boolean => {
    // Show audio control in the first cell of any word
    return words.some(word =>
      word.startRow === rowIndex && word.startCol === colIndex
    );
  };

  // Get the word that starts at this cell
  const getWordAtCell = (rowIndex: number, colIndex: number) => {
    return words.find(word =>
      word.startRow === rowIndex && word.startCol === colIndex
    );
  };

  // Check if this cell's audio is currently playing
  const isCellAudioPlaying = (rowIndex: number, colIndex: number): boolean => {
    if (!selectedWord || !isPlaying) return false;
    return selectedWord.startRow === rowIndex && selectedWord.startCol === colIndex;
  };

  // Check if the puzzle is completed
  const checkPuzzleCompletion = (): boolean => {
    for (const word of words) {
      for (let i = 0; i < word.length; i++) {
        const row = word.direction === 'across' ? word.startRow : word.startRow + i;
        const col = word.direction === 'across' ? word.startCol + i : word.startCol;

        const expectedLetter = word.answer[i];
        const actualLetter = cells[row]?.[col]?.letter;

        if (actualLetter !== expectedLetter) {
          return false;
        }
      }
    }
    return true;
  };

  // Check completion whenever cells change
  useEffect(() => {
    const completed = checkPuzzleCompletion();
    if (completed && !isCompleted) {
      setIsCompleted(true);
      onPuzzleComplete?.();
      console.log('🎉 Puzzle completed!');
    }
  }, [cells, isCompleted, onPuzzleComplete]);
  
  return (
    <div className="crossword-grid mx-auto w-full max-w-2xl">
      <div
        className="grid gap-px p-2 bg-gray-800 border border-gray-600"
        style={{
          gridTemplateColumns: `repeat(${gridSize}, minmax(48px, 1fr))`,
          gridTemplateRows: `repeat(${gridSize}, minmax(48px, 1fr))`,
          aspectRatio: '1',
          width: '100%',
          height: 'auto'
        }}
      >
        {cells.map((row, rowIndex) =>
          row.map((cell, colIndex) => {
            const showAudioControl = shouldShowAudioControl(rowIndex, colIndex);
            const wordAtCell = showAudioControl ? getWordAtCell(rowIndex, colIndex) : null;
            const isThisCellPlaying = isCellAudioPlaying(rowIndex, colIndex);

            return (
              <div
                key={`${rowIndex}-${colIndex}`}
                className={cn(
                  "relative w-full h-full min-h-[48px] cursor-pointer transition-all duration-200",
                  "flex items-center justify-center text-lg font-medium",
                  cell.isBlocked
                    ? "bg-black"
                    : "bg-white hover:bg-gray-100 border border-gray-300",
                  cell.isSelected && "bg-blue-200 border-blue-400",
                  cell.isHighlighted && !cell.isSelected && "bg-blue-100",
                  showAudioControl && "bg-red-100 border-red-400 border-2"
                )}
                onClick={() => !cell.isBlocked && onCellClick(rowIndex, colIndex)}
              >
                {!cell.isBlocked && (
                  <>
                    {cell.number && !showAudioControl && (
                      <span className="absolute top-1 left-1 text-xs text-black font-bold leading-none">
                        {cell.number}
                      </span>
                    )}

                    {showAudioControl && wordAtCell ? (
                      <button
                        onClick={(e) => handleAudioToggle(e, wordAtCell)}
                        className={cn(
                          "w-full h-full flex items-center justify-center",
                          "hover:bg-red-200 transition-colors duration-200",
                          "text-red-600"
                        )}
                        title={`${isThisCellPlaying ? 'Pause' : 'Play'} audio clue for ${wordAtCell.answer}`}
                      >
                        {isThisCellPlaying ? (
                          <Pause className="h-6 w-6" />
                        ) : (
                          <Play className="h-6 w-6" />
                        )}
                        {cell.number && (
                          <span className="absolute top-0 left-0 text-xs text-red-600 font-bold leading-none">
                            {cell.number}
                          </span>
                        )}
                      </button>
                    ) : (
                      <span className="text-black font-bold text-xl">
                        {cell.letter}
                      </span>
                    )}
                  </>
                )}
              </div>
            );
          })
        )}

        {/* Hidden audio element */}
        {currentAudioUrl && (
          <audio
            ref={audioRef}
            src={currentAudioUrl}
            onEnded={handleAudioEnded}
            onError={handleAudioError}
            onLoadedData={handleAudioLoad}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            preload="metadata"
            crossOrigin="anonymous"
          />
        )}
      </div>
    </div>
  );
};