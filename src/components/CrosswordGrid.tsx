import React from 'react';
import { cn } from '@/lib/utils';
import { CrosswordCell, CrosswordWord } from '@/types/crossword';

interface CrosswordGridProps {
  cells: CrosswordCell[][];
  words: CrosswordWord[];
  selectedCell: { row: number; col: number } | null;
  selectedWord: CrosswordWord | null;
  onCellClick: (row: number, col: number) => void;
}

export const CrosswordGrid = ({
  cells,
  words,
  selectedCell,
  selectedWord,
  onCellClick
}: CrosswordGridProps) => {
  const gridSize = cells.length;
  
  return (
    <div className="crossword-grid mx-auto w-full max-w-lg">
      <div 
        className="grid gap-px p-2 bg-gray-800 border border-gray-600"
        style={{
          gridTemplateColumns: `repeat(${gridSize}, minmax(32px, 1fr))`,
          gridTemplateRows: `repeat(${gridSize}, minmax(32px, 1fr))`,
          aspectRatio: '1'
        }}
      >
        {cells.map((row, rowIndex) =>
          row.map((cell, colIndex) => (
            <div
              key={`${rowIndex}-${colIndex}`}
              className={cn(
                "relative w-full h-full min-h-[32px] cursor-pointer transition-all duration-200",
                "flex items-center justify-center text-base font-medium",
                cell.isBlocked 
                  ? "bg-black" 
                  : "bg-white hover:bg-gray-100 border border-gray-300",
                cell.isSelected && "bg-blue-200 border-blue-400",
                cell.isHighlighted && !cell.isSelected && "bg-blue-100"
              )}
              onClick={() => !cell.isBlocked && onCellClick(rowIndex, colIndex)}
            >
              {!cell.isBlocked && (
                <>
                  {cell.number && (
                    <span className="absolute top-1 left-1 text-xs text-black font-bold leading-none">
                      {cell.number}
                    </span>
                  )}
                  <span className="text-black font-bold text-lg">
                    {cell.letter}
                  </span>
                </>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};