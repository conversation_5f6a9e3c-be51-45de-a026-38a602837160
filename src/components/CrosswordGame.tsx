import React, { useEffect } from 'react';
import { CrosswordGrid } from './CrosswordGrid';
import { CrosswordKeyboard } from './CrosswordKeyboard';
import { useCrosswordGame } from '@/hooks/useCrosswordGame';
import { CrosswordPuzzle } from '@/types/crossword';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Play } from 'lucide-react';

interface CrosswordGameProps {
  puzzle: CrosswordPuzzle;
}

export const CrosswordGame = ({ puzzle }: CrosswordGameProps) => {
  const { gameState, selectCell, inputLetter, deleteLetter, loadPuzzle } = useCrosswordGame();

  useEffect(() => {
    loadPuzzle(puzzle);
  }, [puzzle, loadPuzzle]);

  if (!gameState.currentPuzzle) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-muted-foreground">Loading puzzle...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Main game area */}
      <div className="flex-1 flex items-center justify-center p-4 bg-gray-900">
        <div className="text-center">
          {/* Current clue info */}
          {gameState.selectedWord && (
            <div className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-600">
              <div className="text-white text-sm font-medium">
                {gameState.selectedWord.id} {gameState.selectedWord.direction.toUpperCase()}
              </div>
              <div className="text-gray-300 text-xs mt-1">
                {gameState.selectedWord.clue.description || 'Listen to the audio clue'}
              </div>
              <div className="text-gray-400 text-xs mt-1 flex items-center justify-center gap-2">
                <span>Click the</span>
                <span className="inline-flex items-center justify-center w-6 h-6 bg-red-100 border border-red-400 rounded text-red-600">
                  <Play className="h-3 w-3" />
                </span>
                <span>button in the red cell to play audio</span>
              </div>
            </div>
          )}

          <CrosswordGrid
            cells={gameState.currentPuzzle.grid.cells}
            words={gameState.currentPuzzle.words}
            selectedCell={gameState.selectedCell}
            selectedWord={gameState.selectedWord}
            onCellClick={selectCell}
          />
        </div>
      </div>

      {/* Virtual keyboard */}
      <CrosswordKeyboard
        onLetterPress={inputLetter}
        onBackspace={deleteLetter}
        disabled={!gameState.selectedWord}
      />
    </div>
  );
};