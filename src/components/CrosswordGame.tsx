import React, { useEffect } from 'react';
import { CrosswordGrid } from './CrosswordGrid';
import { CrosswordKeyboard } from './CrosswordKeyboard';
import { MediaCluePlayer } from './MediaCluePlayer';
import { useCrosswordGame } from '@/hooks/useCrosswordGame';
import { CrosswordPuzzle } from '@/types/crossword';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CrosswordGameProps {
  puzzle: CrosswordPuzzle;
}

export const CrosswordGame = ({ puzzle }: CrosswordGameProps) => {
  const { gameState, selectCell, inputLetter, deleteLetter, loadPuzzle } = useCrosswordGame();

  useEffect(() => {
    loadPuzzle(puzzle);
  }, [puzzle, loadPuzzle]);

  if (!gameState.currentPuzzle) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-muted-foreground">Loading puzzle...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Media player at top */}
      {gameState.selectedWord && (
        <MediaCluePlayer
          clue={gameState.selectedWord.clue}
          wordNumber={gameState.selectedWord.id}
          direction={gameState.selectedWord.direction}
        />
      )}

      {/* Main game area */}
      <div className="flex-1 flex items-center justify-center p-4 bg-gray-900">
        <CrosswordGrid
          cells={gameState.currentPuzzle.grid.cells}
          words={gameState.currentPuzzle.words}
          selectedCell={gameState.selectedCell}
          selectedWord={gameState.selectedWord}
          onCellClick={selectCell}
        />
      </div>

      {/* Virtual keyboard */}
      <CrosswordKeyboard
        onLetterPress={inputLetter}
        onBackspace={deleteLetter}
        disabled={!gameState.selectedWord}
      />
    </div>
  );
};