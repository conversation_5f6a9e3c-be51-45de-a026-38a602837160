import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Delete } from 'lucide-react';

interface CrosswordKeyboardProps {
  onLetterPress: (letter: string) => void;
  onBackspace: () => void;
  disabled?: boolean;
}

const KEYBOARD_ROWS = [
  ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
  ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
  ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
];

export const CrosswordKeyboard = ({
  onLetterPress,
  onBackspace,
  disabled = false
}: CrosswordKeyboardProps) => {
  return (
    <div className="bg-gray-800 p-3">
      <div className="max-w-sm mx-auto space-y-1">
        {KEYBOARD_ROWS.map((row, rowIndex) => (
          <div 
            key={rowIndex} 
            className="flex justify-center gap-1"
            style={{ marginLeft: rowIndex === 1 ? '1.25rem' : rowIndex === 2 ? '2.5rem' : '0' }}
          >
            {row.map((letter) => (
              <Button
                key={letter}
                variant="outline"
                size="sm"
                className={cn(
                  "min-w-[2.25rem] h-9 p-0 font-medium text-white bg-gray-600 border-gray-500",
                  "hover:bg-gray-500 active:bg-gray-400",
                  "transition-colors duration-150",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => !disabled && onLetterPress(letter)}
                disabled={disabled}
              >
                {letter}
              </Button>
            ))}
          </div>
        ))}
        
        {/* Bottom row with backspace */}
        <div className="flex justify-center gap-1 mt-2">
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "min-w-[4rem] h-9 font-medium text-white bg-gray-600 border-gray-500",
              "hover:bg-gray-500 active:bg-gray-400",
              "transition-colors duration-150",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            onClick={() => !disabled && onBackspace()}
            disabled={disabled}
          >
            <Delete className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};