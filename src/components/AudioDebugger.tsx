import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, AlertCircle, CheckCircle } from 'lucide-react';

interface AudioDebuggerProps {
  audioUrl: string;
  title: string;
}

export const AudioDebugger = ({ audioUrl, title }: AudioDebuggerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [duration, setDuration] = useState<number | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handlePlay = async () => {
    if (!audioRef.current) return;

    try {
      setError(null);
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Audio play error:', err);
      setError(`Failed to play audio: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsPlaying(false);
    }
  };

  const handleLoadedData = () => {
    console.log('✅ Audio loaded successfully:', title);
    setIsLoaded(true);
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleError = (e: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    const audio = e.currentTarget;
    const errorCode = audio.error?.code;
    const errorMessage = audio.error?.message || 'Unknown audio error';
    
    console.error('❌ Audio error:', { title, url: audioUrl, errorCode, errorMessage });
    setError(`Audio error (${errorCode}): ${errorMessage}`);
    setIsLoaded(false);
  };

  const handleEnded = () => {
    setIsPlaying(false);
  };

  return (
    <div className="bg-gray-800 p-4 rounded-lg mb-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-white font-medium">{title}</h3>
        <div className="flex items-center gap-2">
          {isLoaded ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          )}
          <span className="text-xs text-gray-400">
            {isLoaded ? `${duration?.toFixed(1)}s` : 'Loading...'}
          </span>
        </div>
      </div>
      
      <div className="text-xs text-gray-400 mb-3 break-all">
        {audioUrl}
      </div>
      
      <div className="flex items-center gap-3">
        <Button
          onClick={handlePlay}
          disabled={!isLoaded}
          variant="outline"
          size="sm"
          className="text-white border-gray-600 hover:bg-gray-700"
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          {isPlaying ? 'Pause' : 'Play'}
        </Button>
        
        {error && (
          <div className="text-red-400 text-xs flex-1">
            {error}
          </div>
        )}
      </div>
      
      <audio
        ref={audioRef}
        src={audioUrl}
        onLoadedData={handleLoadedData}
        onError={handleError}
        onEnded={handleEnded}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        preload="metadata"
        crossOrigin="anonymous"
      />
    </div>
  );
};
