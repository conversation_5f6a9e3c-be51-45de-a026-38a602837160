import { useState, useCallback } from 'react';
import { CrosswordPuzzle, CrosswordWord, GameState } from '@/types/crossword';

export const useCrosswordGame = (initialPuzzle: CrosswordPuzzle | null = null) => {
  const [gameState, setGameState] = useState<GameState>({
    currentPuzzle: initialPuzzle,
    selectedWord: null,
    selectedCell: null,
    userAnswers: {},
    isCompleted: false,
    startTime: initialPuzzle ? new Date() : null,
    endTime: null
  });

  const selectCell = useCallback((row: number, col: number) => {
    if (!gameState.currentPuzzle) return;

    const puzzle = gameState.currentPuzzle;
    const cell = puzzle.grid.cells[row][col];
    
    if (cell.isBlocked) return;

    // Find words that contain this cell
    const wordsAtCell = puzzle.words.filter(word => {
      if (word.direction === 'across') {
        return row === word.startRow && 
               col >= word.startCol && 
               col < word.startCol + word.length;
      } else {
        return col === word.startCol && 
               row >= word.startRow && 
               row < word.startRow + word.length;
      }
    });

    // Select the word - if current word contains this cell, switch direction
    let selectedWord = wordsAtCell[0];
    if (wordsAtCell.length > 1 && gameState.selectedWord) {
      const currentWordContainsCell = wordsAtCell.find(w => w.id === gameState.selectedWord!.id);
      if (currentWordContainsCell) {
        selectedWord = wordsAtCell.find(w => w.id !== gameState.selectedWord!.id) || wordsAtCell[0];
      }
    }

    setGameState(prev => ({
      ...prev,
      selectedCell: { row, col },
      selectedWord
    }));

    // Update grid highlighting
    updateGridHighlighting(selectedWord);
  }, [gameState.currentPuzzle, gameState.selectedWord]);

  const updateGridHighlighting = (selectedWord: CrosswordWord | null) => {
    if (!gameState.currentPuzzle || !selectedWord) return;

    setGameState(prev => {
      if (!prev.currentPuzzle) return prev;

      const newCells = prev.currentPuzzle.grid.cells.map((row, rowIndex) =>
        row.map((cell, colIndex) => {
          const isSelected = prev.selectedCell?.row === rowIndex && prev.selectedCell?.col === colIndex;
          let isHighlighted = false;

          if (selectedWord) {
            if (selectedWord.direction === 'across') {
              isHighlighted = rowIndex === selectedWord.startRow &&
                            colIndex >= selectedWord.startCol &&
                            colIndex < selectedWord.startCol + selectedWord.length;
            } else {
              isHighlighted = colIndex === selectedWord.startCol &&
                            rowIndex >= selectedWord.startRow &&
                            rowIndex < selectedWord.startRow + selectedWord.length;
            }
          }

          return {
            ...cell,
            isSelected,
            isHighlighted
          };
        })
      );

      return {
        ...prev,
        currentPuzzle: {
          ...prev.currentPuzzle,
          grid: {
            ...prev.currentPuzzle.grid,
            cells: newCells
          }
        }
      };
    });
  };

  const inputLetter = useCallback((letter: string) => {
    if (!gameState.selectedCell || !gameState.selectedWord || !gameState.currentPuzzle) return;

    const { row, col } = gameState.selectedCell;
    const word = gameState.selectedWord;

    // Update the cell with the letter
    setGameState(prev => {
      if (!prev.currentPuzzle || !prev.selectedCell) return prev;

      const newCells = prev.currentPuzzle.grid.cells.map((cellRow, rowIndex) =>
        cellRow.map((cell, colIndex) => {
          if (rowIndex === row && colIndex === col) {
            return { ...cell, letter };
          }
          return cell;
        })
      );

      // Update user answers
      const cellPosition = word.direction === 'across' 
        ? col - word.startCol 
        : row - word.startRow;
      
      const currentAnswer = prev.userAnswers[word.id] || '';
      const newAnswer = currentAnswer.substring(0, cellPosition) + 
                       letter + 
                       currentAnswer.substring(cellPosition + 1);

      const newUserAnswers = {
        ...prev.userAnswers,
        [word.id]: newAnswer
      };

      // Move to next cell in the word
      let nextRow = row;
      let nextCol = col;
      
      if (word.direction === 'across' && col < word.startCol + word.length - 1) {
        nextCol = col + 1;
      } else if (word.direction === 'down' && row < word.startRow + word.length - 1) {
        nextRow = row + 1;
      }

      return {
        ...prev,
        currentPuzzle: {
          ...prev.currentPuzzle,
          grid: {
            ...prev.currentPuzzle.grid,
            cells: newCells
          }
        },
        selectedCell: { row: nextRow, col: nextCol },
        userAnswers: newUserAnswers
      };
    });
  }, [gameState.selectedCell, gameState.selectedWord, gameState.currentPuzzle]);

  const deleteLetter = useCallback(() => {
    if (!gameState.selectedCell || !gameState.selectedWord || !gameState.currentPuzzle) return;

    const { row, col } = gameState.selectedCell;
    const word = gameState.selectedWord;

    setGameState(prev => {
      if (!prev.currentPuzzle || !prev.selectedCell) return prev;

      const newCells = prev.currentPuzzle.grid.cells.map((cellRow, rowIndex) =>
        cellRow.map((cell, colIndex) => {
          if (rowIndex === row && colIndex === col) {
            return { ...cell, letter: '' };
          }
          return cell;
        })
      );

      // Update user answers
      const cellPosition = word.direction === 'across' 
        ? col - word.startCol 
        : row - word.startRow;
      
      const currentAnswer = prev.userAnswers[word.id] || '';
      const newAnswer = currentAnswer.substring(0, cellPosition) + 
                       ' ' + 
                       currentAnswer.substring(cellPosition + 1);

      const newUserAnswers = {
        ...prev.userAnswers,
        [word.id]: newAnswer.trim()
      };

      // Move to previous cell in the word
      let prevRow = row;
      let prevCol = col;
      
      if (word.direction === 'across' && col > word.startCol) {
        prevCol = col - 1;
      } else if (word.direction === 'down' && row > word.startRow) {
        prevRow = row - 1;
      }

      return {
        ...prev,
        currentPuzzle: {
          ...prev.currentPuzzle,
          grid: {
            ...prev.currentPuzzle.grid,
            cells: newCells
          }
        },
        selectedCell: { row: prevRow, col: prevCol },
        userAnswers: newUserAnswers
      };
    });
  }, [gameState.selectedCell, gameState.selectedWord, gameState.currentPuzzle]);

  const loadPuzzle = useCallback((puzzle: CrosswordPuzzle) => {
    setGameState({
      currentPuzzle: puzzle,
      selectedWord: null,
      selectedCell: null,
      userAnswers: {},
      isCompleted: false,
      startTime: new Date(),
      endTime: null
    });
  }, []);

  return {
    gameState,
    selectCell,
    inputLetter,
    deleteLetter,
    loadPuzzle
  };
};