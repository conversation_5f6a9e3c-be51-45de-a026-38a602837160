// Wikimedia Commons API service for fetching audio files
import { ClueMedia } from '@/types/crossword';

export interface WikimediaAudioFile {
  filename: string;
  title: string;
  url: string;
  duration?: number;
  description?: string;
  license?: string;
  category: string;
}

export interface CategoryMember {
  pageid: number;
  ns: number;
  title: string;
}

export interface WikimediaSearchResponse {
  query: {
    categorymembers: CategoryMember[];
  };
}

export interface WikimediaFileResponse {
  title: string;
  file_description_url: string;
  latest: {
    timestamp: string;
    user: string;
  };
  preferred?: {
    url: string;
    width: number;
    height: number;
  };
  original: {
    url: string;
    width?: number;
    height?: number;
  };
  thumbnail?: {
    url: string;
    width: number;
    height: number;
  };
}

export class WikimediaService {
  private readonly baseApiUrl = 'https://commons.wikimedia.org/w/api.php';
  private readonly fileApiUrl = 'https://api.wikimedia.org/core/v1/commons/file/';
  
  // Common audio file extensions supported by Wikimedia Commons
  private readonly audioExtensions = ['.ogg', '.oga', '.wav', '.flac', '.mp3'];

  /**
   * Search for audio files in a specific category
   */
  async searchAudioByCategory(category: string, limit: number = 20): Promise<WikimediaAudioFile[]> {
    try {
      console.log(`🔍 Searching category: ${category}`);

      const params = new URLSearchParams({
        action: 'query',
        list: 'categorymembers',
        cmtitle: `Category:${category}`,
        cmtype: 'file',
        cmlimit: limit.toString(),
        format: 'json',
        origin: '*'
      });

      const url = `${this.baseApiUrl}?${params}`;
      console.log(`📡 API URL: ${url}`);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: WikimediaSearchResponse = await response.json();
      console.log(`📊 API Response:`, data);

      if (!data.query?.categorymembers) {
        console.warn(`No files found in category: ${category}`);
        return [];
      }

      console.log(`📁 Found ${data.query.categorymembers.length} total files`);

      // Filter for audio files only
      const audioFiles = data.query.categorymembers.filter(member =>
        this.isAudioFile(member.title)
      );

      console.log(`🎵 Found ${audioFiles.length} audio files`);

      // Get detailed information for each audio file
      const audioFileDetails = await Promise.all(
        audioFiles.slice(0, 5).map(file => this.getAudioFileDetails(file.title, category))
      );

      const validFiles = audioFileDetails.filter(file => file !== null) as WikimediaAudioFile[];
      console.log(`✅ Successfully processed ${validFiles.length} audio files`);

      return validFiles;
    } catch (error) {
      console.error(`❌ Error searching category ${category}:`, error);
      return [];
    }
  }

  /**
   * Get detailed information about a specific audio file
   */
  async getAudioFileDetails(filename: string, category: string): Promise<WikimediaAudioFile | null> {
    try {
      // Remove 'File:' prefix if present
      const cleanFilename = filename.replace(/^File:/, '');
      
      const response = await fetch(`${this.fileApiUrl}${encodeURIComponent(cleanFilename)}`);
      
      if (!response.ok) {
        console.warn(`Failed to fetch details for ${filename}: ${response.status}`);
        return null;
      }
      
      const data: WikimediaFileResponse = await response.json();
      
      return {
        filename: cleanFilename,
        title: data.title,
        url: data.original.url,
        duration: this.estimateDuration(cleanFilename),
        description: this.extractDescription(data.title),
        license: 'CC-BY-SA', // Most Wikimedia files are CC-BY-SA
        category
      };
    } catch (error) {
      console.error(`Error fetching file details for ${filename}:`, error);
      return null;
    }
  }

  /**
   * Convert WikimediaAudioFile to ClueMedia format
   */
  toClueMedia(audioFile: WikimediaAudioFile, description?: string): ClueMedia {
    return {
      type: 'audio',
      src: audioFile.url,
      duration: audioFile.duration || 5,
      description: description || audioFile.description || `Audio from ${audioFile.category}`
    };
  }

  /**
   * Search multiple categories and return mixed results
   */
  async searchMultipleCategories(categories: string[], filesPerCategory: number = 5): Promise<WikimediaAudioFile[]> {
    const allFiles: WikimediaAudioFile[] = [];
    
    for (const category of categories) {
      const files = await this.searchAudioByCategory(category, filesPerCategory);
      allFiles.push(...files);
    }
    
    return this.shuffleArray(allFiles);
  }

  /**
   * Get curated audio files for crossword puzzles
   */
  async getCuratedAudioFiles(): Promise<WikimediaAudioFile[]> {
    console.log('🎯 Getting curated audio files...');

    // Try to get real files from Wikimedia
    const categories = [
      'Audio files of animal sounds',
      'Sounds of water',
      'Sounds of nature'
    ];

    const realFiles = await this.searchMultipleCategories(categories, 2);

    if (realFiles.length > 0) {
      console.log(`✅ Found ${realFiles.length} real audio files from Wikimedia`);
      return realFiles;
    }

    // Fallback to known working files
    console.log('📋 Using fallback audio files...');
    return this.getFallbackAudioFiles();
  }

  /**
   * Get fallback audio files when Wikimedia API is not available
   */
  private getFallbackAudioFiles(): WikimediaAudioFile[] {
    return [
      {
        filename: 'Wolf_howls.ogg',
        title: 'Wolf howling sound',
        url: 'https://upload.wikimedia.org/wikipedia/commons/8/87/Wolf_howls.ogg',
        duration: 28,
        description: 'Wolf howling',
        license: 'Public Domain',
        category: 'Audio files of animal sounds'
      },
      {
        filename: 'Wolf_howls.mp3',
        title: 'Wolf howling sound (MP3)',
        url: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/8/87/Wolf_howls.ogg/Wolf_howls.ogg.mp3',
        duration: 28,
        description: 'Wolf howling (MP3 format)',
        license: 'Public Domain',
        category: 'Audio files of animal sounds'
      }
    ];
  }

  // Helper methods
  private isAudioFile(filename: string): boolean {
    return this.audioExtensions.some(ext => 
      filename.toLowerCase().includes(ext)
    );
  }

  private estimateDuration(filename: string): number {
    // Simple heuristic - could be improved with actual metadata
    if (filename.toLowerCase().includes('short')) return 3;
    if (filename.toLowerCase().includes('long')) return 10;
    return 5; // Default duration
  }

  private extractDescription(title: string): string {
    // Extract meaningful description from filename
    const cleanTitle = title
      .replace(/^File:/, '')
      .replace(/\.(ogg|oga|wav|flac|mp3)$/i, '')
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
    
    return cleanTitle;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Export singleton instance
export const wikimediaService = new WikimediaService();
