// Simple crossword puzzle generator using Wikimedia audio
import { CrosswordPuzzle, CrosswordWord, CrosswordCell } from '@/types/crossword';
import { wikimediaService, WikimediaAudioFile } from './wikimediaService';

interface WordEntry {
  word: string;
  audioFile: WikimediaAudioFile;
  clue: string;
}

export class PuzzleGenerator {
  // Simple word database that matches common audio files
  private readonly wordDatabase: { [key: string]: string[] } = {
    'Audio files of animal sounds': [
      'DOG', 'CAT', 'COW', 'PIG', 'SHEEP', 'GOAT', 'HORSE', 'DUCK', 'FROG', 'BEE'
    ],
    'Sounds of water': [
      'RAIN', 'WAVE', 'RIVER', 'OCEAN', 'STREAM', 'DROP'
    ],
    'Sounds of nature': [
      'WIND', 'THUNDER', 'BIRD', 'TREE', 'STORM'
    ],
    'Sound signals': [
      'BELL', 'HORN', 'SIREN', 'ALARM', 'WHISTLE'
    ],
    'Sounds of machinery': [
      'ENGINE', 'MOTOR', 'DRILL', 'SAW', 'PUMP'
    ]
  };

  /**
   * Generate a simple crossword puzzle with Wikimedia audio
   */
  async generatePuzzle(theme?: string): Promise<CrosswordPuzzle> {
    console.log('🎵 Generating puzzle with Wikimedia audio...');
    
    // Get audio files from Wikimedia
    const audioFiles = await wikimediaService.getCuratedAudioFiles();
    console.log(`📁 Found ${audioFiles.length} audio files`);
    
    if (audioFiles.length === 0) {
      console.warn('⚠️ No audio files found, falling back to sample puzzle');
      return this.createFallbackPuzzle();
    }

    // Match audio files to words
    const wordEntries = this.matchAudioToWords(audioFiles);
    console.log(`🔤 Matched ${wordEntries.length} words to audio`);

    if (wordEntries.length < 3) {
      console.warn('⚠️ Not enough matches, falling back to sample puzzle');
      return this.createFallbackPuzzle();
    }

    // Create a simple 5x5 grid with the matched words
    return this.createSimplePuzzle(wordEntries.slice(0, 5));
  }

  /**
   * Match audio files to words based on filename and category
   */
  private matchAudioToWords(audioFiles: WikimediaAudioFile[]): WordEntry[] {
    const matches: WordEntry[] = [];

    for (const audioFile of audioFiles) {
      const possibleWords = this.wordDatabase[audioFile.category] || [];
      
      // Try to find a word that matches the audio file
      for (const word of possibleWords) {
        if (this.isLikelyMatch(audioFile, word)) {
          matches.push({
            word,
            audioFile,
            clue: this.generateClue(audioFile, word)
          });
          break; // Only use each audio file once
        }
      }
    }

    return matches;
  }

  /**
   * Check if an audio file likely matches a word
   */
  private isLikelyMatch(audioFile: WikimediaAudioFile, word: string): boolean {
    const filename = audioFile.filename.toLowerCase();
    const wordLower = word.toLowerCase();
    
    // Direct match in filename
    if (filename.includes(wordLower)) return true;
    
    // Category-based matching
    if (audioFile.category === 'Audio files of animal sounds') {
      const animalSounds: { [key: string]: string[] } = {
        'dog': ['bark', 'woof', 'canine'],
        'cat': ['meow', 'purr', 'feline'],
        'cow': ['moo', 'cattle', 'bovine'],
        'pig': ['oink', 'grunt', 'swine'],
        'sheep': ['baa', 'bleat'],
        'horse': ['neigh', 'whinny', 'equine'],
        'duck': ['quack'],
        'bee': ['buzz', 'hum'],
        'frog': ['croak', 'ribbit']
      };
      
      const sounds = animalSounds[wordLower] || [];
      return sounds.some(sound => filename.includes(sound));
    }
    
    return false;
  }

  /**
   * Generate a descriptive clue for the audio
   */
  private generateClue(audioFile: WikimediaAudioFile, word: string): string {
    const category = audioFile.category;
    
    if (category === 'Audio files of animal sounds') {
      return `Listen to this ${word.toLowerCase()} sound`;
    } else if (category === 'Sounds of water') {
      return `The sound of ${word.toLowerCase()}`;
    } else if (category === 'Sounds of nature') {
      return `Natural sound: ${word.toLowerCase()}`;
    } else {
      return `Audio clue for ${word.toLowerCase()}`;
    }
  }

  /**
   * Create a simple 5x5 crossword with the given words
   */
  private createSimplePuzzle(wordEntries: WordEntry[]): CrosswordPuzzle {
    const gridSize = 5;
    const grid: CrosswordCell[][] = Array(gridSize).fill(null).map(() =>
      Array(gridSize).fill(null).map(() => ({
        isBlocked: true,
        letter: '',
        isSelected: false,
        isHighlighted: false
      }))
    );

    const words: CrosswordWord[] = [];
    let wordId = 1;

    // Place words in a simple pattern
    if (wordEntries.length >= 1) {
      // First word across at row 1
      const word1 = wordEntries[0];
      this.placeWordInGrid(grid, word1.word, 1, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 1,
        startCol: 0,
        length: word1.word.length,
        answer: word1.word,
        clue: wikimediaService.toClueMedia(word1.audioFile, word1.clue)
      });
    }

    if (wordEntries.length >= 2) {
      // Second word down at col 2
      const word2 = wordEntries[1];
      this.placeWordInGrid(grid, word2.word, 0, 2, 'down', wordId);
      words.push({
        id: wordId++,
        direction: 'down',
        startRow: 0,
        startCol: 2,
        length: word2.word.length,
        answer: word2.word,
        clue: wikimediaService.toClueMedia(word2.audioFile, word2.clue)
      });
    }

    if (wordEntries.length >= 3) {
      // Third word across at row 3
      const word3 = wordEntries[2];
      this.placeWordInGrid(grid, word3.word, 3, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 3,
        startCol: 0,
        length: word3.word.length,
        answer: word3.word,
        clue: wikimediaService.toClueMedia(word3.audioFile, word3.clue)
      });
    }

    return {
      id: `wikimedia-${Date.now()}`,
      title: 'Wikimedia Audio Crossword',
      difficulty: 'easy',
      description: 'A crossword puzzle with real audio clues from Wikimedia Commons',
      grid: {
        width: gridSize,
        height: gridSize,
        cells: grid
      },
      words
    };
  }

  /**
   * Place a word in the grid
   */
  private placeWordInGrid(
    grid: CrosswordCell[][],
    word: string,
    startRow: number,
    startCol: number,
    direction: 'across' | 'down',
    wordNumber: number
  ): void {
    for (let i = 0; i < word.length; i++) {
      const row = direction === 'across' ? startRow : startRow + i;
      const col = direction === 'across' ? startCol + i : startCol;
      
      if (row < grid.length && col < grid[0].length) {
        grid[row][col] = {
          isBlocked: false,
          letter: '',
          number: i === 0 ? wordNumber : undefined,
          isSelected: false,
          isHighlighted: false
        };
      }
    }
  }

  /**
   * Fallback puzzle when Wikimedia API fails
   */
  private createFallbackPuzzle(): CrosswordPuzzle {
    console.log('📝 Creating fallback puzzle...');
    
    // Return the existing sample puzzle as fallback
    return {
      id: 'fallback-sample',
      title: 'Sample Audio Crossword',
      difficulty: 'easy',
      description: 'A sample crossword with placeholder audio',
      grid: {
        width: 5,
        height: 5,
        cells: [
          [
            { isBlocked: false, letter: '', number: 1, isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', number: 2, isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: false, letter: '', number: 3, isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ]
        ]
      },
      words: [
        {
          id: 1,
          direction: 'across',
          startRow: 0,
          startCol: 0,
          length: 3,
          answer: 'CAT',
          clue: {
            type: 'audio',
            src: '/audio/cat-meow.mp3',
            duration: 3,
            description: 'Listen to this household pet'
          }
        },
        {
          id: 2,
          direction: 'down',
          startRow: 1,
          startCol: 2,
          length: 4,
          answer: 'TREE',
          clue: {
            type: 'audio',
            src: '/audio/wind-in-trees.mp3',
            duration: 5,
            description: 'Sound of wind through this plant'
          }
        },
        {
          id: 3,
          direction: 'across',
          startRow: 2,
          startCol: 0,
          length: 4,
          answer: 'RAIN',
          clue: {
            type: 'audio',
            src: '/audio/rainfall.mp3',
            duration: 6,
            description: 'Weather sound from the sky'
          }
        }
      ]
    };
  }
}

// Export singleton instance
export const puzzleGenerator = new PuzzleGenerator();
