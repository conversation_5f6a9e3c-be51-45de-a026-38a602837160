// Simple crossword puzzle generator using Wikimedia audio
import { CrosswordPuzzle, CrosswordWord, CrosswordCell } from '@/types/crossword';
import { wikimediaService, WikimediaAudioFile } from './wikimediaService';

interface WordEntry {
  word: string;
  audioFile: WikimediaAudioFile;
  clue: string;
}

export class PuzzleGenerator {
  // Simple word database that matches common audio files
  private readonly wordDatabase: { [key: string]: string[] } = {
    'Audio files of animal sounds': [
      'DOG', 'CAT', 'COW', 'PIG', 'SHEEP', 'GOAT', 'HORSE', 'DUCK', 'FROG', 'BEE', 'WOLF'
    ],
    'Sounds of water': [
      'RAIN', 'WAVE', 'RIVER', 'OCEAN', 'STREAM', 'DROP'
    ],
    'Sounds of nature': [
      'WIND', 'THUNDER', 'BIRD', 'TREE', 'STORM'
    ],
    'Sound signals': [
      'BELL', 'HORN', 'SIREN', 'ALARM', 'WHISTLE'
    ],
    'Sounds of machinery': [
      'ENGINE', 'MOTOR', 'DRILL', 'SAW', 'PUMP'
    ]
  };

  /**
   * Generate a simple crossword puzzle with Wikimedia audio
   */
  async generatePuzzle(theme?: string): Promise<CrosswordPuzzle> {
    console.log('🎵 Generating puzzle with Wikimedia audio...');

    try {
      // Get audio files from Wikimedia
      const audioFiles = await wikimediaService.getCuratedAudioFiles();
      console.log(`📁 Found ${audioFiles.length} audio files:`, audioFiles);

      if (audioFiles.length === 0) {
        console.warn('⚠️ No audio files found, falling back to sample puzzle');
        return this.createFallbackPuzzle();
      }

      // Match audio files to words
      const wordEntries = this.matchAudioToWords(audioFiles);
      console.log(`🔤 Matched ${wordEntries.length} words to audio:`, wordEntries);

      if (wordEntries.length < 2) {
        console.warn('⚠️ Not enough matches, falling back to sample puzzle');
        return this.createFallbackPuzzle();
      }

      // Create a simple 5x5 grid with the matched words
      return this.createSimplePuzzle(wordEntries.slice(0, 5));
    } catch (error) {
      console.error('❌ Error generating puzzle:', error);
      console.warn('⚠️ Falling back to sample puzzle due to error');
      return this.createFallbackPuzzle();
    }
  }

  /**
   * Match audio files to words based on filename and category
   */
  private matchAudioToWords(audioFiles: WikimediaAudioFile[]): WordEntry[] {
    const matches: WordEntry[] = [];

    console.log('🔍 Matching audio files to words:', audioFiles);

    // For wolf audio, create specific matches
    for (const audioFile of audioFiles) {
      console.log(`🎵 Processing audio file: ${audioFile.filename}`);

      if (audioFile.filename.toLowerCase().includes('wolf')) {
        console.log('🐺 Found wolf audio, creating WOLF match');
        matches.push({
          word: 'WOLF',
          audioFile,
          clue: 'Listen to this wild animal sound'
        });
        continue;
      }

      const possibleWords = this.wordDatabase[audioFile.category] || [];
      console.log(`📝 Possible words for category "${audioFile.category}":`, possibleWords);

      // Try to find a word that matches the audio file
      for (const word of possibleWords) {
        if (this.isLikelyMatch(audioFile, word)) {
          console.log(`✅ Match found: ${word} for ${audioFile.filename}`);
          matches.push({
            word,
            audioFile,
            clue: this.generateClue(audioFile, word)
          });
          break; // Only use each audio file once
        }
      }
    }

    console.log(`🎯 Total matches found: ${matches.length}`, matches);
    return matches;
  }

  /**
   * Check if an audio file likely matches a word
   */
  private isLikelyMatch(audioFile: WikimediaAudioFile, word: string): boolean {
    const filename = audioFile.filename.toLowerCase();
    const title = audioFile.title.toLowerCase();
    const description = audioFile.description?.toLowerCase() || '';
    const wordLower = word.toLowerCase();

    // Direct match in filename, title, or description
    if (filename.includes(wordLower) || title.includes(wordLower) || description.includes(wordLower)) {
      return true;
    }

    // Category-based matching
    if (audioFile.category === 'Audio files of animal sounds') {
      const animalSounds: { [key: string]: string[] } = {
        'dog': ['bark', 'woof', 'canine', 'dog'],
        'cat': ['meow', 'purr', 'feline', 'cat'],
        'cow': ['moo', 'cattle', 'bovine', 'cow'],
        'pig': ['oink', 'grunt', 'swine', 'pig'],
        'sheep': ['baa', 'bleat', 'sheep'],
        'horse': ['neigh', 'whinny', 'equine', 'horse'],
        'duck': ['quack', 'duck'],
        'bee': ['buzz', 'hum', 'bee'],
        'frog': ['croak', 'ribbit', 'frog']
      };

      const sounds = animalSounds[wordLower] || [];
      return sounds.some(sound =>
        filename.includes(sound) || title.includes(sound) || description.includes(sound)
      );
    }

    return false;
  }

  /**
   * Generate a descriptive clue for the audio
   */
  private generateClue(audioFile: WikimediaAudioFile, word: string): string {
    const category = audioFile.category;
    
    if (category === 'Audio files of animal sounds') {
      return `Listen to this ${word.toLowerCase()} sound`;
    } else if (category === 'Sounds of water') {
      return `The sound of ${word.toLowerCase()}`;
    } else if (category === 'Sounds of nature') {
      return `Natural sound: ${word.toLowerCase()}`;
    } else {
      return `Audio clue for ${word.toLowerCase()}`;
    }
  }

  /**
   * Create a simple 5x5 crossword with the given words
   */
  private createSimplePuzzle(wordEntries: WordEntry[]): CrosswordPuzzle {
    const gridSize = 5;
    const grid: CrosswordCell[][] = Array(gridSize).fill(null).map(() =>
      Array(gridSize).fill(null).map(() => ({
        isBlocked: true,
        letter: '',
        isSelected: false,
        isHighlighted: false
      }))
    );

    const words: CrosswordWord[] = [];
    let wordId = 1;

    // Place words in a simple pattern
    if (wordEntries.length >= 1) {
      // First word across at row 1
      const word1 = wordEntries[0];
      this.placeWordInGrid(grid, word1.word, 1, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 1,
        startCol: 0,
        length: word1.word.length,
        answer: word1.word,
        clue: wikimediaService.toClueMedia(word1.audioFile, word1.clue)
      });
    }

    if (wordEntries.length >= 2) {
      // Second word down at col 2
      const word2 = wordEntries[1];
      this.placeWordInGrid(grid, word2.word, 0, 2, 'down', wordId);
      words.push({
        id: wordId++,
        direction: 'down',
        startRow: 0,
        startCol: 2,
        length: word2.word.length,
        answer: word2.word,
        clue: wikimediaService.toClueMedia(word2.audioFile, word2.clue)
      });
    }

    if (wordEntries.length >= 3) {
      // Third word across at row 3
      const word3 = wordEntries[2];
      this.placeWordInGrid(grid, word3.word, 3, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 3,
        startCol: 0,
        length: word3.word.length,
        answer: word3.word,
        clue: wikimediaService.toClueMedia(word3.audioFile, word3.clue)
      });
    }

    return {
      id: `wikimedia-${Date.now()}`,
      title: 'Wikimedia Audio Crossword',
      difficulty: 'easy',
      description: 'A crossword puzzle with real audio clues from Wikimedia Commons',
      grid: {
        width: gridSize,
        height: gridSize,
        cells: grid
      },
      words
    };
  }

  /**
   * Place a word in the grid
   */
  private placeWordInGrid(
    grid: CrosswordCell[][],
    word: string,
    startRow: number,
    startCol: number,
    direction: 'across' | 'down',
    wordNumber: number
  ): void {
    for (let i = 0; i < word.length; i++) {
      const row = direction === 'across' ? startRow : startRow + i;
      const col = direction === 'across' ? startCol + i : startCol;
      
      if (row < grid.length && col < grid[0].length) {
        grid[row][col] = {
          isBlocked: false,
          letter: '',
          number: i === 0 ? wordNumber : undefined,
          isSelected: false,
          isHighlighted: false
        };
      }
    }
  }

  /**
   * Fallback puzzle when Wikimedia API fails
   */
  private createFallbackPuzzle(): CrosswordPuzzle {
    console.log('📝 Creating fallback puzzle...');
    
    // Return the existing sample puzzle as fallback
    return {
      id: 'fallback-sample',
      title: 'Sample Audio Crossword',
      difficulty: 'easy',
      description: 'A sample crossword with placeholder audio',
      grid: {
        width: 5,
        height: 5,
        cells: [
          [
            { isBlocked: false, letter: '', number: 1, isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', number: 2, isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ]
        ]
      },
      words: [
        {
          id: 1,
          direction: 'across',
          startRow: 0,
          startCol: 0,
          length: 4,
          answer: 'WOLF',
          clue: {
            type: 'audio',
            src: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/8/87/Wolf_howls.ogg/Wolf_howls.ogg.mp3',
            duration: 28,
            description: 'Listen to this wild animal sound'
          }
        },
        {
          id: 2,
          direction: 'down',
          startRow: 0,
          startCol: 3,
          length: 4,
          answer: 'FOWL',
          clue: {
            type: 'audio',
            src: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/8/87/Wolf_howls.ogg/Wolf_howls.ogg.mp3',
            duration: 28,
            description: 'Listen to this wild animal sound (different word)'
          }
        }
      ]
    };
  }
}

// Export singleton instance
export const puzzleGenerator = new PuzzleGenerator();
