// Proper crossword puzzle generator with audio integration
import { CrosswordPuzzle, CrosswordWord, CrosswordCell } from '@/types/crossword';
import { wikimediaService, WikimediaAudioFile } from './wikimediaService';

interface WordEntry {
  word: string;
  audioFile?: WikimediaAudioFile;
  clue: string;
  category?: string;
}

interface WordIntersection {
  word1: string;
  word2: string;
  letter: string;
  word1Index: number;
  word2Index: number;
}

interface PlacedWord {
  word: string;
  startRow: number;
  startCol: number;
  direction: 'across' | 'down';
  intersections: Array<{
    letter: string;
    position: number;
    intersectsWith: string;
  }>;
}

export class PuzzleGenerator {
  // Comprehensive word database organized by sound categories
  private readonly soundWords: { [key: string]: string[] } = {
    'animals': ['CAT', 'DOG', 'COW', 'PIG', 'SHEEP', 'GOAT', 'HORSE', 'DUCK', 'FROG', 'BEE', 'WOLF', 'BIRD', 'LION', 'BEAR'],
    'water': ['RAIN', 'WAVE', 'RIVER', 'OCEAN', 'STREAM', 'DROP', 'SPLASH', 'DRIP'],
    'nature': ['WIND', 'THUNDER', 'STORM', 'TREE', 'LEAF', 'FIRE', 'SNOW'],
    'signals': ['BELL', 'HORN', 'SIREN', 'ALARM', 'WHISTLE', 'BEEP', 'BUZZ'],
    'machinery': ['ENGINE', 'MOTOR', 'DRILL', 'SAW', 'PUMP', 'GEAR', 'TOOL'],
    'music': ['PIANO', 'DRUM', 'FLUTE', 'GUITAR', 'VIOLIN', 'SONG', 'NOTE'],
    'transport': ['CAR', 'TRAIN', 'PLANE', 'BOAT', 'BIKE', 'BUS']
  };

  // All available words for crossword generation
  private readonly allWords: string[] = Object.values(this.soundWords).flat();

  /**
   * Generate a proper crossword puzzle with audio integration
   */
  async generatePuzzle(theme?: string): Promise<CrosswordPuzzle> {
    console.log('🧩 Generating crossword puzzle...');

    try {
      // Step 1: Create a proper crossword grid with intersecting words
      console.log('📐 Step 1: Designing crossword grid...');
      const crosswordWords = this.generateCrosswordGrid();
      console.log(`🔤 Generated crossword with ${crosswordWords.length} words:`, crosswordWords.map(w => w.word));

      // Step 2: Try to find audio for each word
      console.log('🎵 Step 2: Finding audio for crossword words...');
      const wordsWithAudio = await this.findAudioForWords(crosswordWords);
      console.log(`🎧 Found audio for ${wordsWithAudio.filter(w => w.audioFile).length}/${wordsWithAudio.length} words`);

      // Step 3: If we don't have enough audio, try alternative words that maintain intersections
      if (wordsWithAudio.filter(w => w.audioFile).length < crosswordWords.length * 0.7) {
        console.log('🔄 Step 3: Trying alternative words to improve audio coverage...');
        const improvedWords = await this.improveAudioCoverage(crosswordWords);
        console.log(`🎯 Improved coverage: ${improvedWords.filter(w => w.audioFile).length}/${improvedWords.length} words have audio`);
        return this.createPuzzleFromWords(improvedWords);
      }

      return this.createPuzzleFromWords(wordsWithAudio);
    } catch (error) {
      console.error('❌ Error generating puzzle:', error);
      console.warn('⚠️ Falling back to sample puzzle due to error');
      return this.createFallbackPuzzle();
    }
  }

  /**
   * Generate a crossword grid with properly intersecting words
   */
  private generateCrosswordGrid(): PlacedWord[] {
    console.log('🎯 Finding intersecting words...');

    // Find good word pairs that can intersect
    const intersections = this.findWordIntersections();
    console.log(`🔗 Found ${intersections.length} possible intersections`);

    if (intersections.length === 0) {
      // Fallback to simple non-intersecting layout
      return this.createSimpleLayout();
    }

    // Select the best intersection for our puzzle
    const bestIntersection = intersections[0];
    console.log(`✨ Using intersection: ${bestIntersection.word1} × ${bestIntersection.word2} at letter '${bestIntersection.letter}'`);

    // Create placed words from the intersection
    const placedWords: PlacedWord[] = [];

    // Place first word horizontally
    const word1StartCol = 1;
    const word1Row = 2;
    placedWords.push({
      word: bestIntersection.word1,
      startRow: word1Row,
      startCol: word1StartCol,
      direction: 'across',
      intersections: [{
        letter: bestIntersection.letter,
        position: bestIntersection.word1Index,
        intersectsWith: bestIntersection.word2
      }]
    });

    // Place second word vertically, intersecting with first
    const word2Col = word1StartCol + bestIntersection.word1Index;
    const word2StartRow = word1Row - bestIntersection.word2Index;
    placedWords.push({
      word: bestIntersection.word2,
      startRow: word2StartRow,
      startCol: word2Col,
      direction: 'down',
      intersections: [{
        letter: bestIntersection.letter,
        position: bestIntersection.word2Index,
        intersectsWith: bestIntersection.word1
      }]
    });

    // Try to add a third word if possible
    const thirdWord = this.findThirdWord(placedWords);
    if (thirdWord) {
      placedWords.push(thirdWord);
      console.log(`➕ Added third word: ${thirdWord.word}`);
    }

    return placedWords;
  }

  /**
   * Find all possible word intersections
   */
  private findWordIntersections(): WordIntersection[] {
    const intersections: WordIntersection[] = [];

    // Check all pairs of words for common letters
    for (let i = 0; i < this.allWords.length; i++) {
      for (let j = i + 1; j < this.allWords.length; j++) {
        const word1 = this.allWords[i];
        const word2 = this.allWords[j];

        // Find common letters
        for (let pos1 = 0; pos1 < word1.length; pos1++) {
          for (let pos2 = 0; pos2 < word2.length; pos2++) {
            if (word1[pos1] === word2[pos2]) {
              intersections.push({
                word1,
                word2,
                letter: word1[pos1],
                word1Index: pos1,
                word2Index: pos2
              });
            }
          }
        }
      }
    }

    // Sort by quality (prefer intersections in middle of words)
    return intersections.sort((a, b) => {
      const aScore = this.scoreIntersection(a);
      const bScore = this.scoreIntersection(b);
      return bScore - aScore;
    }).slice(0, 20); // Take top 20 intersections
  }

  /**
   * Score an intersection based on how good it is for crossword design
   */
  private scoreIntersection(intersection: WordIntersection): number {
    let score = 0;

    // Prefer intersections not at the edges
    const word1Mid = Math.abs(intersection.word1Index - intersection.word1.length / 2);
    const word2Mid = Math.abs(intersection.word2Index - intersection.word2.length / 2);
    score += (10 - word1Mid) + (10 - word2Mid);

    // Prefer longer words
    score += intersection.word1.length + intersection.word2.length;

    // Prefer common letters (vowels and common consonants)
    const commonLetters = ['A', 'E', 'I', 'O', 'U', 'R', 'S', 'T', 'L', 'N'];
    if (commonLetters.includes(intersection.letter)) {
      score += 5;
    }

    return score;
  }

  /**
   * Create a simple layout when no intersections are found
   */
  private createSimpleLayout(): PlacedWord[] {
    const words = this.allWords.slice(0, 3);
    return [
      {
        word: words[0],
        startRow: 1,
        startCol: 1,
        direction: 'across',
        intersections: []
      },
      {
        word: words[1],
        startRow: 3,
        startCol: 1,
        direction: 'across',
        intersections: []
      }
    ];
  }

  /**
   * Try to find a third word that can intersect with existing words
   */
  private findThirdWord(placedWords: PlacedWord[]): PlacedWord | null {
    // For now, return null - we'll implement this later if needed
    return null;
  }

  /**
   * Check if an audio file likely matches a word
   */
  private isLikelyMatch(audioFile: WikimediaAudioFile, word: string): boolean {
    const filename = audioFile.filename.toLowerCase();
    const title = audioFile.title.toLowerCase();
    const description = audioFile.description?.toLowerCase() || '';
    const wordLower = word.toLowerCase();

    // Direct match in filename, title, or description
    if (filename.includes(wordLower) || title.includes(wordLower) || description.includes(wordLower)) {
      return true;
    }

    // Category-based matching
    if (audioFile.category === 'Audio files of animal sounds') {
      const animalSounds: { [key: string]: string[] } = {
        'dog': ['bark', 'woof', 'canine', 'dog'],
        'cat': ['meow', 'purr', 'feline', 'cat'],
        'cow': ['moo', 'cattle', 'bovine', 'cow'],
        'pig': ['oink', 'grunt', 'swine', 'pig'],
        'sheep': ['baa', 'bleat', 'sheep'],
        'horse': ['neigh', 'whinny', 'equine', 'horse'],
        'duck': ['quack', 'duck'],
        'bee': ['buzz', 'hum', 'bee'],
        'frog': ['croak', 'ribbit', 'frog'],
        'wolf': ['howl', 'wolf', 'lupine'],
        'bird': ['chirp', 'tweet', 'sing', 'bird'],
        'lion': ['roar', 'lion'],
        'bear': ['growl', 'bear']
      };

      const sounds = animalSounds[wordLower] || [];
      return sounds.some(sound =>
        filename.includes(sound) || title.includes(sound) || description.includes(sound)
      );
    }

    return false;
  }

  /**
   * Generate a descriptive clue for the audio
   */
  private generateClue(audioFile: WikimediaAudioFile, word: string): string {
    const category = audioFile.category;
    
    if (category === 'Audio files of animal sounds') {
      return `Listen to this ${word.toLowerCase()} sound`;
    } else if (category === 'Sounds of water') {
      return `The sound of ${word.toLowerCase()}`;
    } else if (category === 'Sounds of nature') {
      return `Natural sound: ${word.toLowerCase()}`;
    } else {
      return `Audio clue for ${word.toLowerCase()}`;
    }
  }

  /**
   * Find audio files for crossword words
   */
  private async findAudioForWords(placedWords: PlacedWord[]): Promise<WordEntry[]> {
    const audioFiles = await wikimediaService.getCuratedAudioFiles();
    console.log(`🎵 Searching audio for words: ${placedWords.map(w => w.word).join(', ')}`);

    const wordsWithAudio: WordEntry[] = [];

    for (const placedWord of placedWords) {
      const audioFile = this.findBestAudioMatch(placedWord.word, audioFiles);

      wordsWithAudio.push({
        word: placedWord.word,
        audioFile,
        clue: audioFile ? this.generateClue(audioFile, placedWord.word) : `Sound related to ${placedWord.word}`,
        category: this.getWordCategory(placedWord.word)
      });

      if (audioFile) {
        console.log(`✅ Found audio for ${placedWord.word}: ${audioFile.filename}`);
      } else {
        console.log(`❌ No audio found for ${placedWord.word}`);
      }
    }

    return wordsWithAudio;
  }

  /**
   * Find the best audio match for a specific word
   */
  private findBestAudioMatch(word: string, audioFiles: WikimediaAudioFile[]): WikimediaAudioFile | undefined {
    console.log(`🔍 Looking for audio match for word: ${word}`);
    console.log(`📁 Available audio files:`, audioFiles.map(af => ({ filename: af.filename, category: af.category })));

    // First, try exact matches
    for (const audioFile of audioFiles) {
      console.log(`🎵 Checking ${audioFile.filename} against ${word}...`);
      if (this.isLikelyMatch(audioFile, word)) {
        console.log(`✅ Found match: ${audioFile.filename} for ${word}`);
        return audioFile;
      }
    }

    console.log(`❌ No audio match found for ${word}`);
    return undefined;
  }

  /**
   * Get the category for a word
   */
  private getWordCategory(word: string): string {
    for (const [category, words] of Object.entries(this.soundWords)) {
      if (words.includes(word)) {
        return category;
      }
    }
    return 'general';
  }

  /**
   * Try to improve audio coverage by replacing words
   */
  private async improveAudioCoverage(placedWords: PlacedWord[]): Promise<WordEntry[]> {
    // For now, just return the original words with audio search
    return this.findAudioForWords(placedWords);
  }

  /**
   * Create puzzle from word entries
   */
  private createPuzzleFromWords(wordEntries: WordEntry[]): CrosswordPuzzle {
    // Convert WordEntry[] to the format expected by createSimplePuzzle
    const legacyWordEntries = wordEntries.map(we => ({
      word: we.word,
      audioFile: we.audioFile!,
      clue: we.clue
    })).filter(we => we.audioFile); // Only include words with audio

    if (legacyWordEntries.length === 0) {
      return this.createFallbackPuzzle();
    }

    return this.createSimplePuzzle(legacyWordEntries);
  }

  /**
   * Create a simple 5x5 crossword with the given words
   */
  private createSimplePuzzle(wordEntries: WordEntry[]): CrosswordPuzzle {
    const gridSize = 5;
    const grid: CrosswordCell[][] = Array(gridSize).fill(null).map(() =>
      Array(gridSize).fill(null).map(() => ({
        isBlocked: true,
        letter: '',
        isSelected: false,
        isHighlighted: false
      }))
    );

    const words: CrosswordWord[] = [];
    let wordId = 1;

    // Place words in a simple pattern
    if (wordEntries.length >= 1) {
      // First word across at row 1
      const word1 = wordEntries[0];
      this.placeWordInGrid(grid, word1.word, 1, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 1,
        startCol: 0,
        length: word1.word.length,
        answer: word1.word,
        clue: wikimediaService.toClueMedia(word1.audioFile, word1.clue)
      });
    }

    if (wordEntries.length >= 2) {
      // Second word down at col 2
      const word2 = wordEntries[1];
      this.placeWordInGrid(grid, word2.word, 0, 2, 'down', wordId);
      words.push({
        id: wordId++,
        direction: 'down',
        startRow: 0,
        startCol: 2,
        length: word2.word.length,
        answer: word2.word,
        clue: wikimediaService.toClueMedia(word2.audioFile, word2.clue)
      });
    }

    if (wordEntries.length >= 3) {
      // Third word across at row 3
      const word3 = wordEntries[2];
      this.placeWordInGrid(grid, word3.word, 3, 0, 'across', wordId);
      words.push({
        id: wordId++,
        direction: 'across',
        startRow: 3,
        startCol: 0,
        length: word3.word.length,
        answer: word3.word,
        clue: wikimediaService.toClueMedia(word3.audioFile, word3.clue)
      });
    }

    return {
      id: `wikimedia-${Date.now()}`,
      title: 'Wikimedia Audio Crossword',
      difficulty: 'easy',
      description: 'A crossword puzzle with real audio clues from Wikimedia Commons',
      grid: {
        width: gridSize,
        height: gridSize,
        cells: grid
      },
      words
    };
  }

  /**
   * Place a word in the grid
   */
  private placeWordInGrid(
    grid: CrosswordCell[][],
    word: string,
    startRow: number,
    startCol: number,
    direction: 'across' | 'down',
    wordNumber: number
  ): void {
    for (let i = 0; i < word.length; i++) {
      const row = direction === 'across' ? startRow : startRow + i;
      const col = direction === 'across' ? startCol + i : startCol;
      
      if (row < grid.length && col < grid[0].length) {
        grid[row][col] = {
          isBlocked: false,
          letter: '',
          number: i === 0 ? wordNumber : undefined,
          isSelected: false,
          isHighlighted: false
        };
      }
    }
  }

  /**
   * Fallback puzzle when Wikimedia API fails
   */
  private createFallbackPuzzle(): CrosswordPuzzle {
    console.log('📝 Creating fallback puzzle...');
    
    // Return the existing sample puzzle as fallback
    return {
      id: 'fallback-sample',
      title: 'Sample Audio Crossword',
      difficulty: 'easy',
      description: 'A sample crossword with placeholder audio',
      grid: {
        width: 5,
        height: 5,
        cells: [
          [
            { isBlocked: false, letter: '', number: 1, isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', number: 2, isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ],
          [
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
            { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
          ]
        ]
      },
      words: [
        {
          id: 1,
          direction: 'across',
          startRow: 0,
          startCol: 0,
          length: 4,
          answer: 'WOLF',
          clue: {
            type: 'audio',
            src: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/8/87/Wolf_howls.ogg/Wolf_howls.ogg.mp3',
            duration: 28,
            description: 'Listen to this wild animal sound'
          }
        },
        {
          id: 2,
          direction: 'down',
          startRow: 0,
          startCol: 3,
          length: 4,
          answer: 'FOWL',
          clue: {
            type: 'audio',
            src: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/8/87/Wolf_howls.ogg/Wolf_howls.ogg.mp3',
            duration: 28,
            description: 'Listen to this wild animal sound (different word)'
          }
        }
      ]
    };
  }
}

// Export singleton instance
export const puzzleGenerator = new PuzzleGenerator();
