import React, { useState, useEffect } from 'react';
import { CrosswordGame } from '@/components/CrosswordGame';
import { AudioDebugger } from '@/components/AudioDebugger';
import { samplePuzzle } from '@/data/samplePuzzle';
import { puzzleGenerator } from '@/services/puzzleGenerator';
import { CrosswordPuzzle } from '@/types/crossword';

const Index = () => {
  const [puzzle, setPuzzle] = useState<CrosswordPuzzle>(samplePuzzle);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDebugger, setShowDebugger] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    const loadWikimediaPuzzle = async () => {
      try {
        console.log('🚀 Loading Wikimedia puzzle...');
        setIsLoading(true);
        setError(null);

        const wikimediaPuzzle = await puzzleGenerator.generatePuzzle();
        console.log('🎮 Puzzle loaded:', wikimediaPuzzle);
        console.log('🎵 Audio URLs in puzzle:', wikimediaPuzzle.words.map(w => ({ answer: w.answer, src: w.clue.src })));
        setPuzzle(wikimediaPuzzle);

        console.log('✅ Wikimedia puzzle loaded successfully!');
      } catch (err) {
        console.error('❌ Failed to load Wikimedia puzzle:', err);
        setError('Failed to load audio puzzle. Using sample puzzle instead.');
        setPuzzle(samplePuzzle);
      } finally {
        setIsLoading(false);
      }
    };

    loadWikimediaPuzzle();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading audio crossword...</p>
          <p className="text-gray-400 text-sm">Fetching sounds from Wikimedia Commons</p>
        </div>
      </div>
    );
  }

  const regeneratePuzzle = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('🔄 Regenerating puzzle...');

      const newPuzzle = await puzzleGenerator.generatePuzzle();
      setPuzzle(newPuzzle);

      console.log('✅ New puzzle generated!');
    } catch (err) {
      console.error('❌ Failed to regenerate puzzle:', err);
      setError('Failed to regenerate puzzle');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {error && (
        <div className="bg-yellow-600 text-white p-3 text-center">
          <p>{error}</p>
        </div>
      )}

      {/* Header with regenerate button */}
      <div className="bg-gray-800 p-4 text-center border-b border-gray-700">
        <h1 className="text-white text-2xl font-bold mb-2">Audio-Visual Crosswords</h1>
        <p className="text-gray-300 text-sm mb-3">Powered by Wikimedia Commons</p>
        <div className="flex gap-3">
          <button
            onClick={regeneratePuzzle}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            {isLoading ? '🔄 Generating...' : '🎲 New Puzzle'}
          </button>
          <button
            onClick={() => setShowDebugger(!showDebugger)}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            {showDebugger ? '🎮 Hide Debug' : '🔧 Debug Audio'}
          </button>
        </div>
      </div>

      {/* Audio Debugger */}
      {showDebugger && (
        <div className="bg-gray-900 p-4">
          <h2 className="text-white text-xl font-bold mb-4">Audio Debug Panel</h2>
          <div className="max-w-4xl mx-auto">
            {puzzle.words.map((word) => (
              <AudioDebugger
                key={word.id}
                audioUrl={word.clue.src}
                title={`${word.id} ${word.direction}: ${word.answer} (${word.clue.description})`}
              />
            ))}
          </div>
        </div>
      )}

      <CrosswordGame puzzle={puzzle} />
    </div>
  );
};

export default Index;
