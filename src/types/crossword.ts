// Crossword game type definitions

export interface ClueMedia {
  type: 'audio' | 'video';
  src: string;
  duration: number;
  description?: string;
}

export interface CrosswordWord {
  id: number;
  direction: 'across' | 'down';
  startRow: number;
  startCol: number;
  length: number;
  answer: string;
  clue: ClueMedia;
}

export interface CrosswordCell {
  isBlocked: boolean;
  letter: string;
  number?: number;
  isSelected: boolean;
  isHighlighted: boolean;
}

export interface CrosswordPuzzle {
  id: string;
  title: string;
  difficulty: 'easy' | 'medium' | 'hard';
  description?: string;
  grid: {
    width: number;
    height: number;
    cells: CrosswordCell[][];
  };
  words: CrosswordWord[];
}

export interface GameState {
  currentPuzzle: CrosswordPuzzle | null;
  selectedWord: CrosswordWord | null;
  selectedCell: { row: number; col: number } | null;
  userAnswers: Record<number, string>;
  isCompleted: boolean;
  startTime: Date | null;
  endTime: Date | null;
}