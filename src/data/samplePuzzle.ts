import { CrosswordPuzzle } from '@/types/crossword';

// Sample puzzle data for demonstration
export const samplePuzzle: CrosswordPuzzle = {
  id: 'sample-001',
  title: 'Nature Sounds & Animals',
  difficulty: 'easy',
  description: 'A beginner-friendly puzzle featuring animal sounds and nature audio',
  grid: {
    width: 7,
    height: 7,
    cells: [
      [
        { isBlocked: false, letter: '', number: 1, isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', number: 2, isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: false, letter: '', number: 3, isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: false, letter: '', number: 4, isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ],
      [
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: true, letter: '', isSelected: false, isHighlighted: false },
        { isBlocked: false, letter: '', isSelected: false, isHighlighted: false }
      ]
    ]
  },
  words: [
    {
      id: 1,
      direction: 'across',
      startRow: 0,
      startCol: 0,
      length: 3,
      answer: 'CAT',
      clue: {
        type: 'audio',
        src: '/audio/cat-meow.mp3',
        duration: 3,
        description: 'Listen to this common household pet'
      }
    },
    {
      id: 2,
      direction: 'across',
      startRow: 0,
      startCol: 4,
      length: 3,
      answer: 'DOG',
      clue: {
        type: 'audio',
        src: '/audio/dog-bark.mp3',
        duration: 2,
        description: 'This animal is known as man\'s best friend'
      }
    },
    {
      id: 3,
      direction: 'across',
      startRow: 2,
      startCol: 0,
      length: 7,
      answer: 'CHICKEN',
      clue: {
        type: 'audio',
        src: '/audio/chicken-cluck.mp3',
        duration: 4,
        description: 'This farm bird lays eggs'
      }
    },
    {
      id: 4,
      direction: 'across',
      startRow: 4,
      startCol: 0,
      length: 7,
      answer: 'THUNDER',
      clue: {
        type: 'audio',
        src: '/audio/thunder.mp3',
        duration: 6,
        description: 'This loud sound comes during storms'
      }
    },
    {
      id: 5,
      direction: 'down',
      startRow: 0,
      startCol: 2,
      length: 5,
      answer: 'CREEK',
      clue: {
        type: 'video',
        src: '/video/stream-flowing.mp4',
        duration: 8,
        description: 'Water flowing in nature'
      }
    },
    {
      id: 6,
      direction: 'down',
      startRow: 0,
      startCol: 4,
      length: 5,
      answer: 'DRONE',
      clue: {
        type: 'audio',
        src: '/audio/bee-buzzing.mp3',
        duration: 5,
        description: 'The continuous sound bees make'
      }
    }
  ]
};